apiVersion: v1
kind: Service
metadata:
  name: test-react-app
  namespace: test-react-app-dev
  labels:
    app: test-react-app
    component: service
    environment: dev
    app-type: react-frontend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: test-react-app
    app.kubernetes.io/name: test-react-app
  sessionAffinity: None
  # Service-specific configurations for different environments
---
# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: test-react-app-headless
  namespace: test-react-app-dev
  labels:
    app: test-react-app
    component: headless-service
    environment: dev
    app-type: react-frontend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: test-react-app
    app.kubernetes.io/name: test-react-app
