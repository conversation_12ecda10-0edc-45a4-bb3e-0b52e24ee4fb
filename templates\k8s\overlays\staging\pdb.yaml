apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: staging
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "staging"
    policy.kubernetes.io/strategy: "balanced"
spec:
  # Staging PDB - Balanced availability during disruptions
  minAvailable: 2  # Keep at least 2 pods running for staging validation
  
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  
  # Staging-specific disruption policy
  # Ensure sufficient pods remain available for staging testing and validation
