apiVersion: v1
kind: ConfigMap
metadata:
  name: test-project-config
  namespace: test-project-dev
  labels:
    app: test-project
    component: config
    environment: dev
    app-type: react-frontend
  annotations:
    config.kubernetes.io/local-config: "true"
data:
  # Application Configuration
  APP_NAME: "test-app"
  PROJECT_ID: "test-project"
  ENVIRONMENT: "dev"
  NODE_ENV: "production"
  PORT: "8080"
  # React Frontend Configuration
  REACT_APP_NAME: "test-app"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_ENVIRONMENT: "dev"
  REACT_APP_API_URL: "http://test-project-service:8080"
  REACT_APP_PUBLIC_URL: "http://test-project.dev.local"
  # Build Configuration
  GENERATE_SOURCEMAP: "false"
  INLINE_RUNTIME_CHUNK: "false"
  IMAGE_INLINE_SIZE_LIMIT: "10000"
  "
  DB_PORT: "5432"
  DB_NAME: "test_project"
  DB_SSL: "false"
  DB_POOL_MIN: "2"
  DB_POOL_MAX: "10"
  DB_TIMEOUT: "10000"
  # SMTP Configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_SECURE: "true"
  SMTP_FROM: "<EMAIL>"
  # OAuth Configuration
  GOOGLE_CALLBACK_URL: "http://test-project.dev.local/auth/google/callback"
  # Session Configuration
  SESSION_SECRET: "default-session-secret"
  SESSION_MAX_AGE: "86400000"
  # Health Check Configuration
  HEALTH_CHECK_PATH: "/"
  HEALTH_CHECK_INTERVAL: "30000"
  # Logging Configuration
  LOG_LEVEL: "debug"
  LOG_FORMAT: "json"
  LOG_TIMESTAMP: "true"
  # Performance Configuration
  MAX_REQUEST_SIZE: "50mb"
  REQUEST_TIMEOUT: "60000"
  # Security Configuration
  HELMET_ENABLED: "false"
  RATE_LIMIT_ENABLED: "false"
  RATE_LIMIT_MAX: "1000"
  RATE_LIMIT_WINDOW: "900000"  # 15 minutes
  # Application Monitoring Configuration (without PLG stack)
  HEALTH_MONITORING_ENABLED: "true"
  HEALTH_MONITORING_PATH: "/"
  APPLICATION_METRICS_ENABLED: "false"
  # Cache Configuration
  CACHE_TTL: "300"
  CACHE_MAX_SIZE: "100"
  # Feature Flags
  FEATURE_REGISTRATION: "true"
  FEATURE_DEBUG_MODE: "true"
  FEATURE_ANALYTICS: "false"
  # Environment-specific overrides
  # Development specific configuration
  DEBUG: "true"
  VERBOSE_LOGGING: "true"
  HOT_RELOAD: "true"
