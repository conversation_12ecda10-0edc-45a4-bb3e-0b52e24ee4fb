apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: {{PROJECT_ID}}-production
  annotations:
    config.kubernetes.io/local-config: "true"

namespace: {{NAMESPACE}}

resources:
  - ../../base
  - hpa.yaml
  - pdb.yaml
  - networkpolicy.yaml
  - podsecuritypolicy.yaml

patchesStrategicMerge:
  - deployment-patch.yaml

commonLabels:
  environment: production
  deployment-strategy: rolling-safe

commonAnnotations:
  deployment.kubernetes.io/environment: "production"
  deployment.kubernetes.io/strategy: "rolling-safe"
  deployment.kubernetes.io/cluster: "e9d23ae8-213c-4746-b379-330f85c0a0cf"

# Production Environment Configuration
replicas:
  - name: {{PROJECT_ID}}
    count: 3  # Optimal for 4 vCPU/8GB cluster with safety margin

# Production-specific image configuration
images:
  - name: app-image
    newName: {{CONTAINER_IMAGE_NAME}}
    newTag: {{CONTAINER_IMAGE_TAG}}

# Production-specific config overrides
configMapGenerator:
  - name: {{PROJECT_ID}}-config
    behavior: merge
    literals:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=info
      - VERBOSE_LOGGING=false
      - HOT_RELOAD=false
      - FEATURE_REGISTRATION=false
      - FEATURE_DEBUG_MODE=false
      - FEATURE_ANALYTICS=true
      - CACHE_TTL=3600
      - CACHE_MAX_SIZE=1000
      - RATE_LIMIT_MAX=100
      - HELMET_ENABLED=true
      - RATE_LIMIT_ENABLED=true
      - TRACING_ENABLED=true
      - PERFORMANCE_MONITORING=true
      - ERROR_REPORTING=true
      - DB_SSL=true
      - DB_POOL_MIN=5
      - DB_POOL_MAX=20
      - DB_TIMEOUT=30000
      - MAX_REQUEST_SIZE=10mb
      - REQUEST_TIMEOUT=30000

# Production-specific secret overrides
secretGenerator:
  - name: {{PROJECT_ID}}-secrets
    behavior: merge
    literals:
      - PROD_ENCRYPTION_KEY=UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgcHJvZHVjdGlvbiBlbmNyeXB0aW9uIGtleQ==  # PLACEHOLDER
