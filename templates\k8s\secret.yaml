apiVersion: v1
kind: Secret
metadata:
  name: {{PROJECT_ID}}-secrets
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: secrets
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
type: Opaque
data:
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  {{else}}
  # Backend Application Secrets

  # Essential Authentication Secrets
  JWT_SECRET: {{JWT_SECRET_B64}}  # {{#if JWT_SECRET}}User-provided JWT secret{{else}}PLACEHOLDER - Update with actual base64 encoded JWT secret{{/if}}

  {{#if ENABLE_DATABASE}}
  # Database Credentials
  DB_USER: {{#if DB_USER_B64}}{{DB_USER_B64}}{{else}}cG9zdGdyZXM={{/if}}  # postgres
  DB_PASSWORD: {{DB_PASSWORD_B64}}  # {{#if DB_PASSWORD}}User-provided DB password{{else}}PLACEHOLDER - Update with actual base64 encoded password{{/if}}
  {{/if}}

  # SMTP Credentials
  SMTP_USER: {{SMTP_USER_B64}}  # {{#if SMTP_USER}}User-provided SMTP username{{else}}PLACEHOLDER - Update with actual base64 encoded SMTP username{{/if}}
  SMTP_PASS: {{SMTP_PASS_B64}}  # {{#if SMTP_PASS}}User-provided SMTP password{{else}}PLACEHOLDER - Update with actual base64 encoded SMTP password{{/if}}

  # OAuth2 Credentials
  GOOGLE_CLIENT_ID: {{GOOGLE_CLIENT_ID_B64}}  # {{#if GOOGLE_CLIENT_ID}}User-provided Google Client ID{{else}}PLACEHOLDER - Update with actual base64 encoded Google Client ID{{/if}}
  GOOGLE_CLIENT_SECRET: {{GOOGLE_CLIENT_SECRET_B64}}  # {{#if GOOGLE_CLIENT_SECRET}}User-provided Google Client Secret{{else}}PLACEHOLDER - Update with actual base64 encoded Google Client Secret{{/if}}
  {{/eq}}

  # Custom Secret Keys (add manually if needed)
  # CUSTOM_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER - Update with actual base64 encoded value
