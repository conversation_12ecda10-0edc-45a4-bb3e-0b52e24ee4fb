apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: staging
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "staging"
    autoscaling.kubernetes.io/strategy: "balanced"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  
  # Staging HPA Configuration - Balanced scaling
  minReplicas: 2    # Minimum for rolling updates
  maxReplicas: 3    # Limited for 4 vCPU/8GB cluster capacity
  
  # Staging scaling behavior - balanced responsiveness
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 33    # Scale down by 33% at most
        periodSeconds: 60
      - type: Pods
        value: 1     # Or scale down by 1 pod
        periodSeconds: 60
      selectPolicy: Min  # Use the more conservative policy
    scaleUp:
      stabilizationWindowSeconds: 120  # 2 minutes
      policies:
      - type: Percent
        value: 50    # Scale up by 50% at most
        periodSeconds: 60
      - type: Pods
        value: 1     # Or scale up by 1 pod
        periodSeconds: 60
      selectPolicy: Max  # Use the more aggressive policy
  
  # Staging metrics - CPU and memory focused with performance metrics
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60  # Scale when CPU > 60%
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70  # Scale when memory > 70%
  
  # Staging-specific metrics for performance validation
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Additional metrics for static content serving performance
  {{else}}
  # Backend applications - Additional metrics for API performance validation
  {{/eq}}
