apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "production"
    policy.kubernetes.io/strategy: "maximum-availability"
spec:
  # Production PDB - Maximum availability and zero downtime
  minAvailable: 2  # Keep at least 2 pods running for zero downtime
  
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  
  # Production-specific disruption policy
  # Ensure maximum availability during cluster maintenance and node updates
  # This works with the zero-downtime rolling update strategy (maxUnavailable: 0%)
