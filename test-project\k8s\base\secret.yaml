apiVersion: v1
kind: Secret
metadata:
  name: test-project-secrets
  namespace: test-project-dev
  labels:
    app: test-project
    component: secrets
    environment: dev
    app-type: react-frontend
  annotations:
    config.kubernetes.io/local-config: "true"
type: Opaque
data:
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  # Build-time API keys (if needed)
  REACT_APP_API_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVBJIGtleQ==  # PLACEHOLDER
  # Analytics keys (if needed)
  REACT_APP_ANALYTICS_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgYW5hbHl0aWNzIGtleQ==  # PLACEHOLDER
