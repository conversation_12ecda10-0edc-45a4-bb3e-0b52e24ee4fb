apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: dev
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "dev"
    autoscaling.kubernetes.io/strategy: "conservative"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  
  # Development HPA Configuration - Conservative scaling
  minReplicas: 2    # Minimum for rolling updates
  maxReplicas: 4    # Limited for 4 vCPU/8GB cluster capacity
  
  # Development scaling behavior - moderate responsiveness
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 50    # Scale down by 50% at most
        periodSeconds: 60
      - type: Pods
        value: 1     # Or scale down by 1 pod
        periodSeconds: 60
      selectPolicy: Min  # Use the more conservative policy
    scaleUp:
      stabilizationWindowSeconds: 60   # 1 minute
      policies:
      - type: Percent
        value: 100   # Scale up by 100% at most
        periodSeconds: 60
      - type: Pods
        value: 2     # Or scale up by 2 pods
        periodSeconds: 60
      selectPolicy: Max  # Use the more aggressive policy
  
  # Development metrics - CPU focused
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70  # Scale when CPU > 70%
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80  # Scale when memory > 80%
  
  # Development-specific metrics (if available)
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Additional metrics for static content serving
  {{else}}
  # Backend applications - Additional metrics for API performance
  {{/eq}}
