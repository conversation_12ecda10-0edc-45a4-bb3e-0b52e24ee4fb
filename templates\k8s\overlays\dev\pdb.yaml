apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: dev
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "dev"
    policy.kubernetes.io/strategy: "development"
spec:
  # Development PDB - Allow more disruption for faster iteration
  minAvailable: 1  # Keep at least 1 pod running
  
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  
  # Development-specific disruption policy
  # Allow more aggressive disruptions for faster deployments and testing
