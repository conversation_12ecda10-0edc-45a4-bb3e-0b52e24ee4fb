apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  annotations:
    deployment.kubernetes.io/revision: "1"
    deployment.kubernetes.io/strategy: "rolling-safe"
spec:
  # Production Rolling Update Strategy - Zero downtime, maximum safety
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0%   # Zero downtime - no pods unavailable
      maxSurge: 33%        # Conservative surge for production safety
  
  # Extended progress deadline for production safety
  progressDeadlineSeconds: 600  # 10 minutes
  
  # Extended revision history for production rollbacks
  revisionHistoryLimit: 10
  
  template:
    metadata:
      annotations:
        deployment.kubernetes.io/environment: "production"
        deployment.kubernetes.io/strategy: "rolling-safe"
        seccomp.security.alpha.kubernetes.io/pod: "runtime/default"
    spec:
      # Production security context - maximum security
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 3000
        fsGroup: 2000
        seccompProfile:
          type: RuntimeDefault
      
      # Extended termination grace period for production
      terminationGracePeriodSeconds: 60
      
      containers:
      - name: {{PROJECT_ID}}
        # Production resource limits - optimized for 4 vCPU/8GB cluster
        resources:
          requests:
            memory: "512Mi"   # Higher request for production stability
            cpu: "300m"       # 0.3 CPU core
          limits:
            memory: "2Gi"     # 2GB limit (within cluster capacity)
            cpu: "1000m"      # 1 CPU core
        
        # Production health checks - comprehensive with extended startup times
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        {{else}}
        startupProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 18  # Allow up to 3 minutes for startup
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        {{/eq}}
        
        # Production security context - maximum security
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 3000
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        
        # Production environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: DEBUG
          value: "false"
        - name: PERFORMANCE_MONITORING
          value: "true"
        - name: ERROR_REPORTING
          value: "true"
        
      # Production node selection - prefer high-performance nodes
      nodeSelector:
        node-type: "high-performance"  # If available
        kubernetes.io/arch: "amd64"
      
      # Production tolerations - standard workloads only
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "high-performance"
        effect: "NoSchedule"
      
      # Production affinity - strict anti-affinity and node preferences
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - {{PROJECT_ID}}
            topologyKey: kubernetes.io/hostname
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - {{PROJECT_ID}}
              topologyKey: topology.kubernetes.io/zone
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - high-performance
          - weight: 50
            preference:
              matchExpressions:
              - key: topology.kubernetes.io/zone
                operator: In
                values:
                - zone-a
                - zone-b
