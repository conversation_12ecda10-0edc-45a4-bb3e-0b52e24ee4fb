apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{PROJECT_ID}}-netpol
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: network-policy
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    networking.kubernetes.io/environment: "production"
    networking.kubernetes.io/policy: "strict-security"
spec:
  podSelector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  
  policyTypes:
  - Ingress
  - Egress
  
  # Production Ingress Rules - Strict security
  ingress:
  # Allow traffic from ingress controllers only
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  # Allow traffic from monitoring systems (restricted)
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
      podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  # Allow traffic from service mesh sidecars
  - from:
    - podSelector:
        matchLabels:
          app: istio-proxy
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  {{#if ENABLE_DATABASE}}
  # Allow traffic from database pods (same namespace only)
  - from:
    - podSelector:
        matchLabels:
          app: {{PROJECT_ID}}-postgres
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  {{/if}}
  
  # Production Egress Rules - Strict outbound control
  egress:
  # Allow DNS resolution (essential)
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
      podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS traffic for external APIs (restricted to specific IPs if possible)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  {{#if ENABLE_DATABASE}}
  # Allow database connections (same namespace only)
  - to:
    - podSelector:
        matchLabels:
          app: {{PROJECT_ID}}-postgres
    ports:
    - protocol: TCP
      port: 5432
  {{/if}}
  
  # Allow SMTP traffic for email services (specific providers)
  - to:
    - namespaceSelector: {}
      podSelector:
        matchLabels:
          app: smtp-relay
    ports:
    - protocol: TCP
      port: 587
  
  # Allow traffic to external SMTP (Gmail, etc.) - consider restricting to specific IPs
  - to: []
    ports:
    - protocol: TCP
      port: 587  # SMTP with STARTTLS
  
  # Allow traffic to monitoring and logging systems
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Prometheus
    - protocol: TCP
      port: 3000  # Grafana
  
  # Allow traffic to service mesh control plane
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 15010  # Pilot
    - protocol: TCP
      port: 15011  # Pilot
  
  # Block all other traffic (default deny)
