apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: {{PROJECT_ID}}-staging
  annotations:
    config.kubernetes.io/local-config: "true"

namespace: {{NAMESPACE}}

resources:
  - ../../base
  - hpa.yaml
  - pdb.yaml
  - networkpolicy.yaml

patchesStrategicMerge:
  - deployment-patch.yaml

commonLabels:
  environment: staging
  deployment-strategy: rolling-controlled

commonAnnotations:
  deployment.kubernetes.io/environment: "staging"
  deployment.kubernetes.io/strategy: "rolling-controlled"
  deployment.kubernetes.io/cluster: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"

# Staging Environment Configuration
replicas:
  - name: {{PROJECT_ID}}
    count: 3  # Higher replica count for staging validation

# Staging-specific image configuration
images:
  - name: app-image
    newName: {{CONTAINER_IMAGE_NAME}}
    newTag: {{CONTAINER_IMAGE_TAG}}

# Staging-specific config overrides
configMapGenerator:
  - name: {{PROJECT_ID}}-config
    behavior: merge
    literals:
      - ENVIRONMENT=staging
      - DEBUG=false
      - LOG_LEVEL=info
      - VERBOSE_LOGGING=true
      - HOT_RELOAD=false
      - FEATURE_REGISTRATION=true
      - FEATURE_DEBUG_MODE=false
      - FEATURE_ANALYTICS=true
      - CACHE_TTL=1800
      - CACHE_MAX_SIZE=500
      - RATE_LIMIT_MAX=500
      - HELMET_ENABLED=true
      - RATE_LIMIT_ENABLED=true
      - TRACING_ENABLED=true
      - PERFORMANCE_MONITORING=true
      - ERROR_REPORTING=true
      - DB_SSL=false
      - DB_POOL_MIN=3
      - DB_POOL_MAX=15
      - DB_TIMEOUT=20000

# Staging-specific secret overrides
secretGenerator:
  - name: {{PROJECT_ID}}-secrets
    behavior: merge
    literals:
      - STAGING_API_KEY=c3RhZ2luZy1hcGkta2V5  # staging-api-key
