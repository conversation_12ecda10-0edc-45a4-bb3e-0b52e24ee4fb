apiVersion: v1
kind: ResourceQuota
metadata:
  name: test-react-app-quota
  namespace: test-react-app-dev
  labels:
    app: test-react-app
    component: resource-quota
    environment: dev
    app-type: react-frontend
  annotations:
    resource.kubernetes.io/environment: "dev"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  # Environment-specific resource quotas optimized for 4 vCPU/8GB cluster capacity
  hard:
    # Development Environment - Conservative resource allocation
    requests.cpu: "1000m"      # 1 CPU core total
    requests.memory: "1Gi"     # 1GB memory total
    limits.cpu: "2000m"        # 2 CPU cores max
    limits.memory: "2Gi"       # 2GB memory max
    pods: "10"                 # Maximum 10 pods
    persistentvolumeclaims: "5"
    services: "5"
    secrets: "10"
    configmaps: "10"
    # Common resource limits across all environments
    replicationcontrollers: "0"  # Use Deployments instead
    resourcequotas: "1"
    count/deployments.apps: "5"
    count/replicasets.apps: "10"
    count/statefulsets.apps: "3"
    count/jobs.batch: "10"
    count/cronjobs.batch: "5"
    count/horizontalpodautoscalers.autoscaling: "5"
    count/poddisruptionbudgets.policy: "5"
    count/networkpolicies.networking.k8s.io: "10"
  # Scope selectors for better resource management
  scopeSelector:
    matchExpressions:
    - operator: In
      scopeName: PriorityClass
      values: ["high-priority", "medium-priority", "low-priority"]
---
apiVersion: v1
kind: LimitRange
metadata:
  name: test-react-app-limits
  namespace: test-react-app-dev
  labels:
    app: test-react-app
    component: limit-range
    environment: dev
    app-type: react-frontend
  annotations:
    resource.kubernetes.io/environment: "dev"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  limits:
  # Pod-level limits
  - type: Pod
    # Development Environment - Flexible limits
    max:
      cpu: "1000m"
      memory: "1Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
  # Container-level limits
  - type: Container
    # Development Environment - Flexible container limits
    default:
      cpu: "200m"
      memory: "256Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    max:
      cpu: "500m"
      memory: "512Mi"
    min:
      cpu: "50m"
      memory: "64Mi"
  # PersistentVolumeClaim limits
  - type: PersistentVolumeClaim
    max:
      storage: "10Gi"
    min:
      storage: "1Gi"
