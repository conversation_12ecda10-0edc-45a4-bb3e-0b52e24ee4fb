#!/usr/bin/env python3
"""
Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters
Python version for cross-platform compatibility
"""

import argparse
import os
import sys
import re
import base64
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m",
        "ERROR": "\033[31m",
        "WARNING": "\033[33m",
        "INFO": "\033[36m"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    try:
        print(f"{color}[{status_type}] {message}{reset}")
    except UnicodeEncodeError:
        # Fallback for systems with encoding issues
        print(f"[{status_type}] {message}")


def validate_project_id(project_id):
    """Validate project ID format"""
    if not re.match(r'^[a-z0-9-]+$', project_id):
        print_status(f"Invalid project ID format: {project_id}", "ERROR")
        print_status("Project ID must be lowercase alphanumeric with hyphens only", "ERROR")
        return False
    return True


def get_environment_config(environment, replicas):
    """Get environment-specific configuration"""
    if replicas == 0:
        replica_map = {"dev": 1, "staging": 2, "production": 3}
        replicas = replica_map.get(environment, 1)
    
    config_map = {
        "dev": {
            "memory_request": "256Mi",
            "memory_limit": "512Mi",
            "cpu_request": "100m",
            "cpu_limit": "500m"
        },
        "staging": {
            "memory_request": "512Mi",
            "memory_limit": "1Gi",
            "cpu_request": "200m",
            "cpu_limit": "1000m"
        },
        "production": {
            "memory_request": "1Gi",
            "memory_limit": "2Gi",
            "cpu_request": "500m",
            "cpu_limit": "2000m"
        }
    }
    
    config = config_map.get(environment, config_map["dev"])
    config["replicas"] = replicas
    
    return config


def get_cluster_config(environment):
    """Get cluster configuration based on environment"""
    # Map environments to specific DOKS clusters
    # ArgoCD is running on cluster 158b6a47-3e7e-4dca-af0f-05a6e07115af (management)
    # Applications deployed to: dev/staging → 6be4e15d-52f9-431d-84ec-ec8cad0dff2d, production → e9d23ae8-213c-4746-b379-330f85c0a0cf
    cluster_mappings = {
        "dev": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "doks-target-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "staging": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "doks-target-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "production": {
            "server": "https://e9d23ae8-213c-4746-b379-330f85c0a0cf.k8s.ondigitalocean.com",
            "name": "doks-production-cluster",
            "cluster_id": "e9d23ae8-213c-4746-b379-330f85c0a0cf"
        }
    }

    return cluster_mappings.get(environment, cluster_mappings["production"])


def replace_template_variables(content, variables):
    """Replace template variables in content with comprehensive Handlebars-style processing"""
    result = content

    # First, replace all simple template variables
    for key, value in variables.items():
        placeholder = f"{{{{{key}}}}}"
        result = result.replace(placeholder, str(value))

    # Handle complex conditional blocks with proper Handlebars-style processing
    result = process_handlebars_conditionals(result, variables)

    return result


def process_handlebars_conditionals(content, variables):
    """Process Handlebars-style conditional blocks with improved nested handling"""
    result = content

    # Handle nested conditionals by processing from innermost to outermost
    max_iterations = 20  # Increased to handle more complex nesting
    iteration = 0

    while iteration < max_iterations:
        iteration += 1
        original_result = result

        # Process #eq conditionals first (innermost)
        result = process_eq_conditionals(result, variables)

        # Process #if conditionals
        result = process_if_conditionals(result, variables)

        # Clean up any remaining malformed template syntax
        result = cleanup_malformed_templates(result)

        # If no changes were made, we're done
        if result == original_result:
            break

    return result


def find_matching_block_end(content, start_pos, open_tag, close_tag):
    """Find the matching closing tag for a conditional block, handling nesting"""
    depth = 1
    pos = start_pos

    while pos < len(content) and depth > 0:
        # Look for the next occurrence of either open or close tag
        next_open = content.find(open_tag, pos)
        next_close = content.find(close_tag, pos)

        if next_close == -1:
            # No more closing tags found
            return -1

        if next_open != -1 and next_open < next_close:
            # Found another opening tag before the closing tag
            depth += 1
            pos = next_open + len(open_tag)
        else:
            # Found a closing tag
            depth -= 1
            if depth == 0:
                return next_close
            pos = next_close + len(close_tag)

    return -1


def process_eq_conditionals(content, variables):
    """Process {{#eq VARIABLE 'value'}}...{{else}}...{{/eq}} conditionals with proper nesting support"""
    result = content

    # Process innermost conditionals first by finding the shortest blocks
    max_iterations = 50
    iteration = 0

    while iteration < max_iterations:
        iteration += 1
        original_result = result

        # Find all {{#eq}} patterns
        eq_starts = []
        for match in re.finditer(r'\{\{#eq\s+(\w+)\s+[\'"]([^\'"]*)[\'\"]\}\}', result):
            eq_starts.append((match.start(), match.end(), match.group(1), match.group(2)))

        if not eq_starts:
            break

        # Process the first (innermost) conditional found
        start_pos, end_start_tag, var_name, expected_value = eq_starts[0]

        # Find the matching {{/eq}}
        close_pos = find_matching_block_end(result, end_start_tag, '{{#eq', '{{/eq}}')
        if close_pos == -1:
            break

        # Extract the content between tags
        block_content = result[end_start_tag:close_pos]

        # Check if there's an {{else}} in this block (not in nested blocks)
        else_pos = block_content.find('{{else}}')
        nested_eq_pos = block_content.find('{{#eq')

        # Only consider {{else}} if it's not inside a nested block
        if else_pos != -1 and (nested_eq_pos == -1 or else_pos < nested_eq_pos):
            true_content = block_content[:else_pos]
            false_content = block_content[else_pos + 8:]  # 8 = len('{{else}}')
        else:
            true_content = block_content
            false_content = ""

        # Determine which content to use
        actual_value = str(variables.get(var_name, ""))
        if actual_value == expected_value:
            replacement = true_content
        else:
            replacement = false_content

        # Replace the entire conditional block
        result = result[:start_pos] + replacement + result[close_pos + 7:]  # 7 = len('{{/eq}}')

        # If no changes were made, break to avoid infinite loop
        if result == original_result:
            break

    return result


def process_if_conditionals(content, variables):
    """Process {{#if VARIABLE}}...{{else}}...{{/if}} conditionals with improved handling"""
    result = content

    # Pattern to match {{#if VARIABLE}}...{{else}}...{{/if}}
    if_pattern = r'\{\{#if\s+(\w+)\}\}(.*?)\{\{else\}\}(.*?)\{\{/if\}\}'

    def replace_if_conditional(match):
        var_name = match.group(1)
        true_content = match.group(2)
        false_content = match.group(3)

        var_value = variables.get(var_name, "")

        # Check if variable is truthy
        if is_truthy(var_value):
            return true_content.strip()
        else:
            return false_content.strip()

    # Process from innermost to outermost by finding the shortest matches first
    while re.search(if_pattern, result, flags=re.DOTALL):
        result = re.sub(if_pattern, replace_if_conditional, result, flags=re.DOTALL, count=1)

    # Handle {{#if VARIABLE}}...{{/if}} without else
    if_pattern_no_else = r'\{\{#if\s+(\w+)\}\}(.*?)\{\{/if\}\}'

    def replace_if_conditional_no_else(match):
        var_name = match.group(1)
        content_block = match.group(2)

        var_value = variables.get(var_name, "")

        # Check if variable is truthy
        if is_truthy(var_value):
            return content_block.strip()
        else:
            return ""

    # Process from innermost to outermost
    while re.search(if_pattern_no_else, result, flags=re.DOTALL):
        result = re.sub(if_pattern_no_else, replace_if_conditional_no_else, result, flags=re.DOTALL, count=1)

    return result


def cleanup_malformed_templates(content):
    """Clean up any remaining malformed template syntax"""
    result = content

    # Remove any remaining standalone template blocks that weren't processed
    patterns_to_remove = [
        r'\{\{else\}\}',  # Standalone else blocks
        r'\{\{/eq\}\}',   # Standalone closing eq blocks
        r'\{\{/if\}\}',   # Standalone closing if blocks
        r'\{\{#eq\s+\w+\s+[\'"][^\'"]*[\'\"]\}\}',  # Orphaned opening eq blocks
        r'\{\{#if\s+\w+\}\}',  # Orphaned opening if blocks
    ]

    for pattern in patterns_to_remove:
        result = re.sub(pattern, '', result, flags=re.MULTILINE)

    # Clean up excessive whitespace and empty lines
    result = re.sub(r'\n\s*\n\s*\n', '\n\n', result)  # Multiple empty lines to double
    result = re.sub(r'^\s*\n', '', result, flags=re.MULTILINE)  # Leading empty lines

    return result


def is_truthy(value):
    """Check if a value is truthy in Handlebars context"""
    if value is None or value == "":
        return False
    if isinstance(value, str):
        return value.lower() not in ["false", "0", ""]
    if isinstance(value, bool):
        return value
    if isinstance(value, (int, float)):
        return value != 0
    return bool(value)


def encode_base64(value):
    """Encode a string value to base64"""
    if isinstance(value, str):
        return base64.b64encode(value.encode('utf-8')).decode('utf-8')
    return base64.b64encode(str(value).encode('utf-8')).decode('utf-8')


def extract_template_variables(content):
    """Extract all template variables from content"""
    # Find all {{VARIABLE}} patterns
    simple_vars = re.findall(r'\{\{([A-Z_][A-Z0-9_]*)\}\}', content)

    # Find variables in conditional blocks
    if_vars = re.findall(r'\{\{#if\s+([A-Z_][A-Z0-9_]*)\}\}', content)
    eq_vars = re.findall(r'\{\{#eq\s+([A-Z_][A-Z0-9_]*)\s+', content)

    # Combine all variables
    all_vars = set(simple_vars + if_vars + eq_vars)
    return sorted(list(all_vars))


def validate_template_syntax(content, template_file):
    """Validate template syntax for common issues"""
    errors = []
    warnings = []

    # Check for unmatched conditional blocks
    if_count = len(re.findall(r'\{\{#if\s+\w+\}\}', content))
    endif_count = len(re.findall(r'\{\{/if\}\}', content))
    if if_count != endif_count:
        errors.append(f"Unmatched {{#if}} blocks: {if_count} opening, {endif_count} closing")

    eq_count = len(re.findall(r'\{\{#eq\s+\w+\s+[\'"][^\'"]*[\'\"]\}\}', content))
    endeq_count = len(re.findall(r'\{\{/eq\}\}', content))
    if eq_count != endeq_count:
        errors.append(f"Unmatched {{#eq}} blocks: {eq_count} opening, {endeq_count} closing")

    # Check for malformed conditional syntax
    malformed_if = re.findall(r'\{\{#if\s*\}\}|\{\{#if\s+\}\}', content)
    if malformed_if:
        errors.append(f"Malformed {{#if}} blocks found: {len(malformed_if)}")

    malformed_eq = re.findall(r'\{\{#eq\s*\}\}|\{\{#eq\s+\w+\s*\}\}', content)
    if malformed_eq:
        errors.append(f"Malformed {{#eq}} blocks found: {len(malformed_eq)}")

    # Check for nested conditionals that might be problematic
    nested_pattern = r'\{\{#(?:if|eq)[^}]*\}\}[^{]*\{\{#(?:if|eq)[^}]*\}\}'
    nested_matches = re.findall(nested_pattern, content, re.DOTALL)
    if nested_matches:
        warnings.append(f"Found {len(nested_matches)} nested conditional blocks - ensure proper processing order")

    return errors, warnings


def validate_template_variables(content, variables, template_file):
    """Validate that all template variables are defined"""
    template_vars = extract_template_variables(content)
    missing_vars = []

    for var in template_vars:
        if var not in variables:
            missing_vars.append(var)

    return missing_vars


def validate_template_file(template_file, variables):
    """Comprehensive template file validation"""
    if not os.path.exists(template_file):
        return False, [f"Template file not found: {template_file}"], []

    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return False, [f"Error reading template file: {e}"], []

    errors = []
    warnings = []

    # Syntax validation
    syntax_errors, syntax_warnings = validate_template_syntax(content, template_file)
    errors.extend(syntax_errors)
    warnings.extend(syntax_warnings)

    # Variable validation
    missing_vars = validate_template_variables(content, variables, template_file)
    if missing_vars:
        errors.append(f"Missing template variables: {', '.join(missing_vars)}")

    # Basic YAML structure check (if it's a YAML file)
    if template_file.endswith('.yaml') or template_file.endswith('.yml'):
        if not content.strip().startswith(('apiVersion:', 'kind:', '{{#if')):
            warnings.append("Template doesn't start with expected YAML structure")

    return len(errors) == 0, errors, warnings


def process_template(template_file, output_file, variables):
    """Process a template file and generate output"""
    if not os.path.exists(template_file):
        print_status(f"Template file not found: {template_file}", "WARNING")
        return False

    print_status(f"Processing template: {os.path.basename(template_file)}", "INFO")

    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Debug: Show key variables for troubleshooting
        if 'configmap' in template_file.lower():
            print_status(f"DEBUG: APP_TYPE = '{variables.get('APP_TYPE', 'NOT_SET')}'", "INFO")

        # Replace template variables
        content = replace_template_variables(content, variables)

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Write to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print_status(f"Generated: {output_file}", "SUCCESS")
        return True

    except Exception as e:
        print_status(f"Error processing template {template_file}: {e}", "ERROR")
        return False


def requires_database(app_type):
    """
    Determine if an application type requires a database.

    Args:
        app_type (str): The application type

    Returns:
        bool: True if the application type requires a database
    """
    # Frontend applications typically don't need databases
    frontend_types = ['react-frontend', 'vue-frontend', 'angular-frontend', 'static-frontend']

    # Backend and full-stack applications typically need databases
    backend_types = ['springboot-backend', 'node-backend', 'python-backend', 'api', 'microservice', 'full-stack']

    # Special cases
    if app_type in frontend_types:
        return False
    elif app_type in backend_types:
        return True
    elif app_type in ['web-app', 'worker']:
        return True  # These might need databases depending on implementation
    elif app_type == 'database':
        return False  # Database applications don't need additional databases
    else:
        # Default to requiring database for unknown types (safer approach)
        return True


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters"
    )

    parser.add_argument('--app-name', required=True, help='Application name')
    parser.add_argument('--project-id', required=True, help='Project identifier (lowercase alphanumeric with hyphens)')
    parser.add_argument('--environment', required=True, choices=['dev', 'staging', 'production', 'feature'], help='Target environment')
    parser.add_argument('--docker-image', required=True, help='Docker image repository')
    parser.add_argument('--docker-tag', required=True, help='Docker image tag')
    parser.add_argument('--application-type', '--app-type', default='springboot-backend',
                       choices=['react-frontend', 'vue-frontend', 'angular-frontend', 'static-frontend', 'springboot-backend', 'node-backend', 'python-backend', 'web-app', 'api', 'microservice', 'worker', 'database', 'full-stack'],
                       help='Application type')
    parser.add_argument('--container-port', type=int, default=8080, help='Container port')
    parser.add_argument('--replicas', type=int, default=0, help='Number of replicas (0 = auto)')
    parser.add_argument('--enable-database', action='store_true', help='Force enable database setup')
    parser.add_argument('--disable-database', action='store_true', help='Disable database setup')
    parser.add_argument('--health-check-path', help='Health check endpoint path')
    parser.add_argument('--source-repo', default='', help='Source repository')
    parser.add_argument('--source-branch', default='', help='Source branch')
    parser.add_argument('--commit-sha', default='', help='Commit SHA')
    parser.add_argument('--secrets-encoded', default='', help='Base64 encoded secrets JSON')
    parser.add_argument('--output-dir', default='.', help='Output directory')

    args = parser.parse_args()

    # Handle database enable/disable logic
    if args.disable_database:
        args.enable_database = False
    elif not args.enable_database:
        # Auto-determine database requirement based on application type
        args.enable_database = requires_database(args.application_type)

    # Set default health check path based on application type
    # Note: Spring Boot backends use TCP probes by default for reliability
    if not args.health_check_path:
        health_check_defaults = {
            'react-frontend': '/',
            'vue-frontend': '/',
            'angular-frontend': '/',
            'static-frontend': '/',
            'springboot-backend': '/actuator/health',  # For reference, but TCP probes are used
            'node-backend': '/health',
            'python-backend': '/health',
            'web-app': '/',
            'api': '/health',
            'microservice': '/health',
            'worker': '/health',
            'full-stack': '/health',
            'database': '/health'
        }
        args.health_check_path = health_check_defaults.get(args.application_type, '/health')

    return args


def ensure_directory_structure(project_id, environment):
    """Ensure the complete k8s directory structure exists"""
    base_path = Path(f"{project_id}/k8s")
    overlays_path = base_path / "overlays" / environment
    base_k8s_path = base_path / "base"
    
    # Create all necessary directories
    overlays_path.mkdir(parents=True, exist_ok=True)
    base_k8s_path.mkdir(parents=True, exist_ok=True)
    
    print(f"Created directory structure: {overlays_path}")
    print(f"Created directory structure: {base_k8s_path}")
    
    return overlays_path, base_k8s_path

def copy_and_process_k8s_templates(template_vars, overlays_path, base_path):
    """Copy and process k8s templates to the correct structure"""
    environment = template_vars['ENVIRONMENT']
    
    # Copy base templates
    base_templates = [
        'deployment-rolling.yaml',
        'service.yaml',
        'configmap.yaml',
        'secret.yaml',
        'resourcequota.yaml',
        'kustomization.yaml'
    ]
    
    for template_file in base_templates:
        src_path = f"templates/k8s/base/{template_file}"
        if os.path.exists(src_path):
            dst_path = base_path / template_file
            process_template(src_path, str(dst_path), template_vars)
    
    # Copy overlay templates
    overlay_templates = [
        'kustomization.yaml',
        'deployment-patch.yaml',
        'service-patch.yaml'
    ]
    
    for template_file in overlay_templates:
        src_path = f"templates/k8s/overlays/{environment}/{template_file}"
        if os.path.exists(src_path):
            dst_path = overlays_path / template_file
            process_template(src_path, str(dst_path), template_vars)
        else:
            # Fallback to generic overlay template
            generic_src = f"templates/k8s/overlays/generic/{template_file}"
            if os.path.exists(generic_src):
                dst_path = overlays_path / template_file
                process_template(generic_src, str(dst_path), template_vars)

def main():
    """Main function"""
    args = parse_arguments()

    try:
        print_status("Starting manifest generation from CI/CD parameters", "INFO")
        print_status(f"Application: {args.app_name}", "INFO")
        print_status(f"Project ID: {args.project_id}", "INFO")
        print_status(f"Environment: {args.environment}", "INFO")
        print_status(f"Docker Image: {args.docker_image}:{args.docker_tag}", "INFO")

        # Validate inputs
        if not validate_project_id(args.project_id):
            sys.exit(1)

        # Get environment configuration
        env_config = get_environment_config(args.environment, args.replicas)

        # Get cluster configuration
        cluster_config = get_cluster_config(args.environment)

        # Set up paths
        project_dir = os.path.join(args.output_dir, args.project_id)
        argocd_dir = os.path.join(project_dir, "argocd")
        k8s_dir = os.path.join(project_dir, "k8s")

        # Create directories
        os.makedirs(argocd_dir, exist_ok=True)
        os.makedirs(k8s_dir, exist_ok=True)

        # Process encoded secrets if provided
        secrets_data = {}
        if args.secrets_encoded:
            try:
                import json
                decoded_secrets = base64.b64decode(args.secrets_encoded).decode('utf-8')
                secrets_data = json.loads(decoded_secrets)
                print_status("Successfully decoded secrets from payload", "SUCCESS")
            except Exception as e:
                print_status(f"Warning: Failed to decode secrets: {e}", "WARNING")
                print_status("Using default secret values", "WARNING")

        # Build template variables dictionary
        template_vars = {
            'APP_NAME': args.app_name,
            'PROJECT_ID': args.project_id,
            'NAMESPACE': f"{args.project_id}-{args.environment}",
            'CONTAINER_IMAGE': f"{args.docker_image}:{args.docker_tag}",
            'CONTAINER_IMAGE_NAME': args.docker_image,
            'CONTAINER_IMAGE_TAG': args.docker_tag,
            'DOCKER_IMAGE': args.docker_image,
            'DOCKER_TAG': args.docker_tag,
            'ENVIRONMENT': args.environment,
            'APP_TYPE': args.application_type,
            'APPLICATION_TYPE': args.application_type,
            'CONTAINER_PORT': str(args.container_port),
            'REPLICAS': str(env_config['replicas']),
            'MEMORY_REQUEST': env_config['memory_request'],
            'MEMORY_LIMIT': env_config['memory_limit'],
            'CPU_REQUEST': env_config['cpu_request'],
            'CPU_LIMIT': env_config['cpu_limit'],
            'CLUSTER_SERVER': cluster_config["server"],
            'CLUSTER_NAME': cluster_config["name"],
            'CLUSTER_ID': cluster_config["cluster_id"],
            'ENABLE_DATABASE': str(args.enable_database).lower(),
            'HEALTH_CHECK_PATH': args.health_check_path,
            'SOURCE_REPO': args.source_repo,
            'SOURCE_BRANCH': args.source_branch,
            'COMMIT_SHA': args.commit_sha,
            'DB_USER': secrets_data.get('DB_USER', 'postgres'),
            'DB_NAME': args.project_id.replace('-', '_'),
            'DB_HOST': f"{args.project_id}-postgres",
            'DB_PASSWORD': secrets_data.get('DB_PASSWORD', 'password'),
            'JWT_SECRET': secrets_data.get('JWT_SECRET', 'supersecretkey'),
            'SMTP_USER': secrets_data.get('SMTP_USER', '<EMAIL>'),
            'SMTP_PASS': secrets_data.get('SMTP_PASS', 'fqactehafmzlltzz'),
            'GOOGLE_CLIENT_ID': secrets_data.get('GOOGLE_CLIENT_ID', '1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com'),
            'GOOGLE_CLIENT_SECRET': secrets_data.get('GOOGLE_CLIENT_SECRET', '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'),
            'SERVICE_TYPE': 'ClusterIP',
            'NODE_PORT': '',
            'INGRESS_ENABLED': 'false',
            'INGRESS_HOST': '',
            'INGRESS_PATH': '/',
            # Missing variables identified from template analysis
            'API_URL': f"http://{args.project_id}-service:{args.container_port}",
            'APP_URL': f"http://{args.project_id}.{args.environment}.local",
            'APP_VERSION': '1.0.0',
            'CORS_ORIGINS': 'http://localhost:3000,http://localhost:3001',
            'ENABLE_INGRESS': 'false',
            'ENABLE_PVC': 'false',
            'GOOGLE_REDIRECT_URI': f"http://{args.project_id}.{args.environment}.local/auth/google/callback",
            'JAVA_XMS': '256m',
            'JAVA_XMX': '512m',
            'JWT_EXPIRATION': '24h',
            'OAUTH_REDIRECT_URIS': f"http://{args.project_id}.{args.environment}.local/auth/google/callback",
            'OAUTH_SCOPES': 'openid,profile,email',
            'PUBLIC_URL': f"http://{args.project_id}.{args.environment}.local",
            'PVC_SIZE': '1Gi',
            'SMTP_FROM': '<EMAIL>',
            'SMTP_HOST': 'smtp.gmail.com',
            'SMTP_PORT': '587',
            'SMTP_SECURE': 'true',
            'STORAGE_SIZE': '5Gi',
            'SESSION_SECRET': secrets_data.get('SESSION_SECRET', 'default-session-secret'),
            'SESSION_MAX_AGE': '86400000',  # 24 hours in milliseconds
            'GOOGLE_CALLBACK_URL': f"http://{args.project_id}.{args.environment}.local/auth/google/callback"
        }

        # Add base64 encoded values for secrets
        template_vars.update({
            'DB_USER_B64': encode_base64(template_vars['DB_USER']),
            'DB_PASSWORD_B64': encode_base64(template_vars['DB_PASSWORD']),
            'JWT_SECRET_B64': encode_base64(template_vars['JWT_SECRET']),
            'SMTP_USER_B64': encode_base64(template_vars['SMTP_USER']),
            'SMTP_PASS_B64': encode_base64(template_vars['SMTP_PASS']),
            'GOOGLE_CLIENT_ID_B64': encode_base64(template_vars['GOOGLE_CLIENT_ID']),
            'GOOGLE_CLIENT_SECRET_B64': encode_base64(template_vars['GOOGLE_CLIENT_SECRET']),
            'SESSION_SECRET_B64': encode_base64(template_vars['SESSION_SECRET']),
            # Additional secret placeholders
            'ADDITIONAL_SECRETS': '',  # Will be populated from GitHub issue if provided
            'AWS_ACCESS_KEY_ID_B64': '',
            'AWS_SECRET_ACCESS_KEY_B64': '',
            'DEBUG_TOKEN_B64': '',
            'ENCRYPTION_KEY_B64': '',
            'PROD_ENCRYPTION_KEY_B64': '',
            'REACT_APP_ANALYTICS_KEY_B64': '',
            'REACT_APP_API_KEY_B64': '',
            'REDIS_PASSWORD_B64': '',
            'SENTRY_DSN_B64': '',
            'STAGING_API_KEY_B64': '',
            'STRIPE_SECRET_KEY_B64': ''
        })

        print_status("Template variables prepared", "SUCCESS")

        # Ensure directory structure exists
        overlays_path, base_path = ensure_directory_structure(args.project_id, args.environment)
        
        # Copy and process k8s templates
        copy_and_process_k8s_templates(template_vars, overlays_path, base_path)

        # Define main templates to process
        templates = [
            {'source': 'templates/argocd/application.yaml', 'target': os.path.join(argocd_dir, 'application.yaml')},
            {'source': 'templates/argocd/project.yaml', 'target': os.path.join(argocd_dir, 'project.yaml')},
            {'source': 'templates/k8s/namespace.yaml', 'target': os.path.join(k8s_dir, 'namespace.yaml')}
        ]

        # Add database templates if enabled
        if args.enable_database:
            print_status(f"Database enabled for application type '{args.application_type}' - including PostgreSQL components", "INFO")
            templates.extend([
                {'source': 'templates/k8s/postgres-deployment.yaml', 'target': os.path.join(k8s_dir, 'postgres-deployment.yaml')},
                {'source': 'templates/k8s/postgres-service.yaml', 'target': os.path.join(k8s_dir, 'postgres-service.yaml')},
                {'source': 'templates/k8s/postgres-pvc.yaml', 'target': os.path.join(k8s_dir, 'postgres-pvc.yaml')}
            ])
        else:
            print_status(f"Database disabled for application type '{args.application_type}' - skipping PostgreSQL components", "INFO")

        # Process templates
        success_count = 0
        for template in templates:
            if process_template(template['source'], template['target'], template_vars):
                success_count += 1
            else:
                print_status(f"Failed to process template: {template['source']}", "WARNING")

        print_status(f"Successfully processed {success_count}/{len(templates)} templates", "SUCCESS")
        print_status(f"Project directory: {project_dir}", "INFO")
        print_status(f"ArgoCD manifests: {argocd_dir}", "INFO")
        print_status(f"Kubernetes manifests: {k8s_dir}", "INFO")

        if success_count == len(templates):
            print_status("Manifest generation completed successfully!", "SUCCESS")
        else:
            print_status("Manifest generation completed with warnings", "WARNING")

    except Exception as e:
        print_status(f"Error in manifest generation: {str(e)}", "ERROR")
        import traceback
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()





