apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: test-project-base
  annotations:
    config.kubernetes.io/local-config: "true"
resources:
  - deployment-rolling.yaml
  - service.yaml
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
commonLabels:
  app: test-project
  app.kubernetes.io/name: test-project
  app.kubernetes.io/part-of: test-project
  app.kubernetes.io/managed-by: argocd
  environment: dev
  app-type: react-frontend
commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"
namespace: test-project-dev
images:
  - name: app-image
    newName: test/image
    newTag: latest
replicas:
  - name: test-project
    count: 1
configMapGenerator:
  - name: test-project-config
    behavior: merge
    literals:
      - ENVIRONMENT=dev
      - APP_NAME=test-app
      - PROJECT_ID=test-project
secretGenerator:
  - name: test-project-secrets
    behavior: merge
    type: Opaque
