apiVersion: v1
kind: Service
metadata:
  name: test-project
  namespace: test-project-dev
  labels:
    app: test-project
    component: service
    environment: dev
    app-type: react-frontend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: test-project
    app.kubernetes.io/name: test-project
  sessionAffinity: None
  # Service-specific configurations for different environments
---
# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: test-project-headless
  namespace: test-project-dev
  labels:
    app: test-project
    component: headless-service
    environment: dev
    app-type: react-frontend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: test-project
    app.kubernetes.io/name: test-project
