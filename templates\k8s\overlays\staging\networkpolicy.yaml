apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{PROJECT_ID}}-netpol
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: network-policy
    environment: staging
    app-type: {{APP_TYPE}}
  annotations:
    networking.kubernetes.io/environment: "staging"
    networking.kubernetes.io/policy: "controlled-access"
spec:
  podSelector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  
  policyTypes:
  - Ingress
  - Egress
  
  # Staging Ingress Rules - Controlled access
  ingress:
  # Allow traffic from other pods in the same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: {{NAMESPACE}}
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  # Allow traffic from ingress controllers
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  # Allow traffic from monitoring systems
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  
  {{#if ENABLE_DATABASE}}
  # Allow traffic from database pods
  - from:
    - podSelector:
        matchLabels:
          app: {{PROJECT_ID}}-postgres
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  {{/if}}
  
  # Staging Egress Rules - Controlled outbound access
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS traffic for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow HTTP traffic for external APIs (if needed)
  - to: []
    ports:
    - protocol: TCP
      port: 80
  
  {{#if ENABLE_DATABASE}}
  # Allow database connections
  - to:
    - podSelector:
        matchLabels:
          app: {{PROJECT_ID}}-postgres
    ports:
    - protocol: TCP
      port: 5432
  {{/if}}
  
  # Allow SMTP traffic for email services
  - to: []
    ports:
    - protocol: TCP
      port: 587  # SMTP with STARTTLS
    - protocol: TCP
      port: 465  # SMTP over SSL
  
  # Allow traffic to other services in the same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: {{NAMESPACE}}
  
  # Allow traffic to system services
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
