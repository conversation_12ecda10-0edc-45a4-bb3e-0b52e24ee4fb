apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{PROJECT_ID}}-sa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: service-account
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    security.kubernetes.io/environment: "production"
    security.kubernetes.io/policy: "restricted"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{PROJECT_ID}}-role
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: rbac
    environment: production
    app-type: {{APP_TYPE}}
rules:
# Minimal permissions for production application
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
  resourceNames: ["{{PROJECT_ID}}-*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{PROJECT_ID}}-rolebinding
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: rbac
    environment: production
    app-type: {{APP_TYPE}}
subjects:
- kind: ServiceAccount
  name: {{PROJECT_ID}}-sa
  namespace: {{NAMESPACE}}
roleRef:
  kind: Role
  name: {{PROJECT_ID}}-role
  apiGroup: rbac.authorization.k8s.io
---
# Pod Security Standards - Restricted Profile for Production
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{PROJECT_ID}}-security-config
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: security-config
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    security.kubernetes.io/policy: "restricted"
    security.kubernetes.io/profile: "production"
data:
  # Pod Security Standards Configuration
  security-profile: "restricted"
  
  # Security Context Requirements
  runAsNonRoot: "true"
  runAsUser: "1000"
  runAsGroup: "3000"
  fsGroup: "2000"
  
  # Capability Requirements
  allowedCapabilities: ""
  requiredDropCapabilities: "ALL"
  
  # Security Profile Requirements
  seccompProfile: "runtime/default"
  seLinuxOptions: "restricted"
  
  # Volume Requirements
  allowedVolumeTypes: "configMap,secret,emptyDir,projected,downwardAPI"
  
  # Network Requirements
  allowPrivilegeEscalation: "false"
  readOnlyRootFilesystem: "true"
  
  # Resource Requirements
  resourceQuotaEnabled: "true"
  limitRangeEnabled: "true"
  
  # Monitoring and Auditing
  auditingEnabled: "true"
  monitoringEnabled: "true"
  
  # Compliance
  complianceProfile: "CIS-Kubernetes-Benchmark"
  securityScanningEnabled: "true"
