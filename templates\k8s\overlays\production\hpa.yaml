apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "production"
    autoscaling.kubernetes.io/strategy: "conservative"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  
  # Production HPA Configuration - Conservative scaling for stability
  minReplicas: 2    # Minimum for zero-downtime rolling updates
  maxReplicas: 3    # Limited for 4 vCPU/8GB cluster capacity with safety margin
  
  # Production scaling behavior - conservative and stable
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes - very conservative
      policies:
      - type: Percent
        value: 25    # Scale down by 25% at most
        periodSeconds: 120
      - type: Pods
        value: 1     # Or scale down by 1 pod
        periodSeconds: 120
      selectPolicy: Min  # Use the more conservative policy
    scaleUp:
      stabilizationWindowSeconds: 300  # 5 minutes - careful scaling up
      policies:
      - type: Percent
        value: 33    # Scale up by 33% at most
        periodSeconds: 120
      - type: Pods
        value: 1     # Or scale up by 1 pod
        periodSeconds: 120
      selectPolicy: Min  # Use the more conservative policy for production
  
  # Production metrics - conservative thresholds for stability
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50  # Scale when CPU > 50% (conservative)
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 60  # Scale when memory > 60% (conservative)
  
  # Production-specific metrics for performance and reliability
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Additional metrics for production static content serving
  {{else}}
  # Backend applications - Additional metrics for production API performance
  {{/eq}}
