apiVersion: v1
kind: Service
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: service
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: {{SERVICE_TYPE}}
  {{#if NODE_PORT}}
  {{#eq SERVICE_TYPE 'NodePort'}}
  ports:
  - port: {{CONTAINER_PORT}}
    targetPort: {{CONTAINER_PORT}}
    nodePort: {{NODE_PORT}}
    protocol: TCP
    name: http
  {{/eq}}
  {{else}}
  ports:
  - port: {{CONTAINER_PORT}}
    targetPort: {{CONTAINER_PORT}}
    protocol: TCP
    name: http
  {{/if}}
  selector:
    app: {{PROJECT_ID}}
    app.kubernetes.io/name: {{PROJECT_ID}}
  sessionAffinity: None
  # Service-specific configurations for different environments
  {{#eq ENVIRONMENT 'production'}}
  # Production: Enable session affinity for better performance
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800  # 3 hours
  {{/eq}}
---
{{#eq APP_TYPE 'react-frontend'}}
# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: {{PROJECT_ID}}-headless
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: headless-service
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: {{CONTAINER_PORT}}
    targetPort: {{CONTAINER_PORT}}
    protocol: TCP
    name: http
  selector:
    app: {{PROJECT_ID}}
    app.kubernetes.io/name: {{PROJECT_ID}}
{{/eq}}
