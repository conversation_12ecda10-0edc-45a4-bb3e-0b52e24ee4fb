apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: test-react-app-base
  annotations:
    config.kubernetes.io/local-config: "true"
resources:
  - deployment-rolling.yaml
  - service.yaml
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
commonLabels:
  app: test-react-app
  app.kubernetes.io/name: test-react-app
  app.kubernetes.io/part-of: test-react-app
  app.kubernetes.io/managed-by: argocd
  environment: dev
  app-type: react-frontend
commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"
namespace: test-react-app-dev
images:
  - name: app-image
    newName: test/react-app
    newTag: v1.0.0
replicas:
  - name: test-react-app
    count: 1
configMapGenerator:
  - name: test-react-app-config
    behavior: merge
    literals:
      - ENVIRONMENT=dev
      - APP_NAME=Test React App
      - PROJECT_ID=test-react-app
secretGenerator:
  - name: test-react-app-secrets
    behavior: merge
    type: Opaque
