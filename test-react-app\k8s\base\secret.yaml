apiVersion: v1
kind: Secret
metadata:
  name: test-react-app-secrets
  namespace: test-react-app-dev
  labels:
    app: test-react-app
    component: secrets
    environment: dev
    app-type: react-frontend
  annotations:
    config.kubernetes.io/local-config: "true"
type: Opaque
data:
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  # Build-time API keys (if needed)
  REACT_APP_API_KEY: 
  UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgcHJvZHVjdGlvbiBlbmNyeXB0aW9uIGtleQ==  # PLACEHOLDER
